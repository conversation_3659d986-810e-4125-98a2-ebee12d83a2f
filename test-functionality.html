<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Funcionalidades Reino Calculator</title>
    
    <!-- CSS do Reino Calculator via CDN -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v1.0.1/dist/index.css">
    
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        
        .test-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .test-checkbox {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .test-checkbox.selected-asset {
            background: #e8f5e8;
            border-color: #28a745;
        }
        
        .debug-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .counter_ativos {
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Teste de Funcionalidades - Reino Calculator</h1>
        
        <div id="loading-status" class="status info">
            ⏳ Carregando Reino Calculator...
        </div>
        
        <div id="success-status" class="status success" style="display: none;">
            ✅ Reino Calculator carregado com sucesso!
        </div>
        
        <div id="error-status" class="status error" style="display: none;">
            ❌ Erro ao carregar Reino Calculator
        </div>
    </div>

    <!-- Teste da Seção 2 - Asset Selection -->
    <div class="test-container">
        <h2>📋 Teste: Asset Selection (Seção 2)</h2>
        
        <div class="_2-section-calc-ativos">
            <p>Contador de ativos selecionados: <span class="counter_ativos">(0)</span></p>
            
            <div class="test-section">
                <h3>Ativos de Teste:</h3>
                
                <div class="ativos_item test-checkbox" ativo-category="Renda Fixa" ativo-product="CDB">
                    <span>CDB - Renda Fixa</span>
                </div>
                
                <div class="ativos_item test-checkbox" ativo-category="Renda Variável" ativo-product="Ações">
                    <span>Ações - Renda Variável</span>
                </div>
                
                <div class="ativos_item test-checkbox" ativo-category="Fundos" ativo-product="FII">
                    <span>FII - Fundos</span>
                </div>
                
                <button class="ativos_clean-button">Limpar Seleções</button>
            </div>
        </div>
    </div>

    <!-- Teste da Seção 3 - Patrimony Allocation -->
    <div class="test-container">
        <h2>💰 Teste: Patrimony Allocation (Seção 3)</h2>
        
        <div class="_3-section-patrimonio-alocation">
            <div class="test-section">
                <h3>Itens de Patrimônio (filtrados por seleção):</h3>
                
                <div class="patrimonio_interactive_item" ativo-category="Renda Fixa" ativo-product="CDB" style="display: none;">
                    <h4>CDB - Renda Fixa</h4>
                    <input type="text" class="currency-input" input-settings="receive" value="R$ 0,00">
                    <input type="range" class="slider" min="0" max="1" step="0.01" value="0">
                    <span class="porcentagem-calculadora">0%</span>
                </div>
                
                <div class="patrimonio_interactive_item" ativo-category="Renda Variável" ativo-product="Ações" style="display: none;">
                    <h4>Ações - Renda Variável</h4>
                    <input type="text" class="currency-input" input-settings="receive" value="R$ 0,00">
                    <input type="range" class="slider" min="0" max="1" step="0.01" value="0">
                    <span class="porcentagem-calculadora">0%</span>
                </div>
                
                <div class="patrimonio_interactive_item" ativo-category="Fundos" ativo-product="FII" style="display: none;">
                    <h4>FII - Fundos</h4>
                    <input type="text" class="currency-input" input-settings="receive" value="R$ 0,00">
                    <input type="range" class="slider" min="0" max="1" step="0.01" value="0">
                    <span class="porcentagem-calculadora">0%</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Debug Info -->
    <div class="test-container">
        <h2>🔍 Informações de Debug</h2>
        
        <button onclick="runDiagnostics()">🧪 Executar Diagnósticos</button>
        <button onclick="testAssetSelection()">📋 Testar Seleção de Ativos</button>
        <button onclick="clearDebug()">🗑️ Limpar Debug</button>
        
        <div id="debug-info" class="debug-info"></div>
    </div>

    <!-- JavaScript do Reino Calculator via CDN -->
    <script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v1.0.1/dist/index.js"></script>
    
    <script>
        let debugLog = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.push(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
            updateDebugDisplay();
            console.log(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
        }
        
        function updateDebugDisplay() {
            const debugElement = document.getElementById('debug-info');
            if (debugElement) {
                debugElement.textContent = debugLog.join('\n');
                debugElement.scrollTop = debugElement.scrollHeight;
            }
        }
        
        function clearDebug() {
            debugLog = [];
            updateDebugDisplay();
        }
        
        function runDiagnostics() {
            log('🔍 Iniciando diagnósticos...', 'info');
            
            // Verificar se ReinoCalculator está disponível
            if (window.ReinoCalculator) {
                log('✅ ReinoCalculator está disponível globalmente', 'success');
                log(`📊 Sistemas disponíveis: ${Object.keys(window.ReinoCalculator.systems).join(', ')}`, 'info');
                
                // Verificar sistema de asset selection
                if (window.ReinoCalculator.systems.assetSelectionFilter) {
                    const assetSystem = window.ReinoCalculator.systems.assetSelectionFilter;
                    log(`📋 AssetSelectionFilter - Inicializado: ${assetSystem.isInitialized}`, 'info');
                    log(`📋 AssetSelectionFilter - Ativos selecionados: ${assetSystem.selectedAssets?.size || 0}`, 'info');
                } else {
                    log('❌ AssetSelectionFilter não encontrado', 'error');
                }
                
                // Verificar outros sistemas críticos
                const criticalSystems = ['currencyControl', 'patrimonySync', 'webflowButton'];
                criticalSystems.forEach(systemName => {
                    const system = window.ReinoCalculator.systems[systemName];
                    if (system) {
                        log(`✅ ${systemName} - Disponível`, 'success');
                    } else {
                        log(`❌ ${systemName} - Não encontrado`, 'error');
                    }
                });
                
            } else {
                log('❌ ReinoCalculator não está disponível globalmente', 'error');
            }
            
            // Verificar elementos DOM
            const section2 = document.querySelector('._2-section-calc-ativos');
            const section3 = document.querySelector('._3-section-patrimonio-alocation');
            
            log(`🏗️ Seção 2 encontrada: ${!!section2}`, section2 ? 'success' : 'error');
            log(`🏗️ Seção 3 encontrada: ${!!section3}`, section3 ? 'success' : 'error');
            
            // Verificar checkboxes
            const checkboxes = document.querySelectorAll('.asset-checkbox');
            log(`☑️ Checkboxes encontrados: ${checkboxes.length}`, checkboxes.length > 0 ? 'success' : 'warning');
            
            // Verificar CSS
            const testElement = document.querySelector('.ativos_item');
            if (testElement) {
                const styles = window.getComputedStyle(testElement);
                log(`🎨 CSS aplicado - Display: ${styles.display}, Cursor: ${styles.cursor}`, 'info');
            }
            
            log('🔍 Diagnósticos concluídos', 'info');
        }
        
        function testAssetSelection() {
            log('🧪 Testando seleção de ativos...', 'info');
            
            const testItems = document.querySelectorAll('.test-checkbox');
            log(`📋 Itens de teste encontrados: ${testItems.length}`, 'info');
            
            testItems.forEach((item, index) => {
                const category = item.getAttribute('ativo-category');
                const product = item.getAttribute('ativo-product');
                log(`📋 Item ${index + 1}: ${product} - ${category}`, 'info');
                
                // Simular clique
                setTimeout(() => {
                    item.click();
                    log(`🖱️ Clique simulado em: ${product}`, 'info');
                }, (index + 1) * 1000);
            });
        }
        
        // Verificar carregamento
        let loadTimeout;
        
        function checkLoading() {
            if (window.ReinoCalculator) {
                document.getElementById('loading-status').style.display = 'none';
                document.getElementById('success-status').style.display = 'block';
                log('✅ Reino Calculator carregado com sucesso!', 'success');
                
                // Executar diagnósticos automáticos
                setTimeout(runDiagnostics, 1000);
                
                clearTimeout(loadTimeout);
            } else {
                log('⏳ Aguardando carregamento do Reino Calculator...', 'info');
            }
        }
        
        // Verificar a cada segundo por 10 segundos
        loadTimeout = setInterval(checkLoading, 1000);
        
        // Timeout após 10 segundos
        setTimeout(() => {
            if (!window.ReinoCalculator) {
                document.getElementById('loading-status').style.display = 'none';
                document.getElementById('error-status').style.display = 'block';
                log('❌ Timeout: Reino Calculator não carregou em 10 segundos', 'error');
                clearInterval(loadTimeout);
            }
        }, 10000);
        
        // Escutar eventos do sistema
        document.addEventListener('reinoCalculatorReady', (event) => {
            log('🎉 Evento reinoCalculatorReady recebido', 'success');
            log(`📊 Detalhes: ${JSON.stringify(event.detail, null, 2)}`, 'info');
        });
        
        document.addEventListener('assetSelectionChanged', (event) => {
            log(`📋 Asset selection changed: ${event.detail.selectedCount} ativos selecionados`, 'info');
        });
        
        // Log inicial
        log('🚀 Página de teste carregada', 'info');
        log('📡 Aguardando carregamento do Reino Calculator via CDN...', 'info');
    </script>
</body>
</html>
