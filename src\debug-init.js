// Debug initialization helper for CDN troubleshooting
console.log('🔧 [DEBUG] Reino Calculator Debug Init loaded');

// Enhanced logging for CDN debugging
window.ReinoDebug = {
    logs: [],
    
    log(message, type = 'info', data = null) {
        const timestamp = new Date().toISOString();
        const logEntry = {
            timestamp,
            type,
            message,
            data,
            stack: new Error().stack
        };
        
        this.logs.push(logEntry);
        
        const emoji = {
            info: 'ℹ️',
            success: '✅',
            warning: '⚠️',
            error: '❌',
            debug: '🔍'
        };
        
        console.log(`${emoji[type] || 'ℹ️'} [${timestamp}] ${message}`, data || '');
        
        // Dispatch custom event for external listeners
        document.dispatchEvent(new CustomEvent('reinoDebugLog', {
            detail: logEntry
        }));
    },
    
    checkDOMReady() {
        this.log('Checking DOM ready state...', 'debug');
        this.log(`Document ready state: ${document.readyState}`, 'info');
        this.log(`DOM content loaded: ${document.readyState !== 'loading'}`, 'info');
        
        if (document.readyState === 'loading') {
            this.log('DOM still loading, adding event listener...', 'warning');
            document.addEventListener('DOMContentLoaded', () => {
                this.log('DOMContentLoaded event fired', 'success');
            });
        } else {
            this.log('DOM already ready', 'success');
        }
    },
    
    checkRequiredElements() {
        this.log('Checking required DOM elements...', 'debug');
        
        const requiredSelectors = [
            '._2-section-calc-ativos',
            '._3-section-patrimonio-alocation',
            '.ativos_item',
            '.patrimonio_interactive_item'
        ];
        
        const results = {};
        requiredSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            results[selector] = elements.length;
            this.log(`Found ${elements.length} elements for: ${selector}`, 
                elements.length > 0 ? 'success' : 'warning');
        });
        
        return results;
    },
    
    checkSystemInitialization() {
        this.log('Checking system initialization...', 'debug');
        
        if (window.ReinoCalculator) {
            this.log('ReinoCalculator global object found', 'success');
            
            if (window.ReinoCalculator.systems) {
                const systems = Object.keys(window.ReinoCalculator.systems);
                this.log(`Available systems: ${systems.join(', ')}`, 'info');
                
                systems.forEach(systemName => {
                    const system = window.ReinoCalculator.systems[systemName];
                    const isInitialized = system && system.isInitialized;
                    this.log(`System ${systemName}: ${isInitialized ? 'initialized' : 'not initialized'}`, 
                        isInitialized ? 'success' : 'warning');
                });
            } else {
                this.log('ReinoCalculator.systems not found', 'error');
            }
        } else {
            this.log('ReinoCalculator global object not found', 'error');
        }
    },
    
    testAssetSelection() {
        this.log('Testing asset selection system...', 'debug');
        
        const assetSystem = window.ReinoCalculator?.systems?.assetSelectionFilter;
        if (!assetSystem) {
            this.log('Asset selection system not available', 'error');
            return false;
        }
        
        this.log(`Asset system initialized: ${assetSystem.isInitialized}`, 
            assetSystem.isInitialized ? 'success' : 'error');
        this.log(`Selected assets count: ${assetSystem.selectedAssets?.size || 0}`, 'info');
        
        // Check for checkboxes
        const checkboxes = document.querySelectorAll('.asset-checkbox');
        this.log(`Asset checkboxes found: ${checkboxes.length}`, 
            checkboxes.length > 0 ? 'success' : 'warning');
        
        return assetSystem.isInitialized;
    },
    
    runFullDiagnostic() {
        this.log('🚀 Starting full diagnostic...', 'info');
        
        this.checkDOMReady();
        
        setTimeout(() => {
            this.checkRequiredElements();
            this.checkSystemInitialization();
            this.testAssetSelection();
            
            this.log('🏁 Full diagnostic completed', 'success');
            this.log(`Total log entries: ${this.logs.length}`, 'info');
        }, 1000);
    },
    
    exportLogs() {
        const logsText = this.logs.map(log => 
            `[${log.timestamp}] ${log.type.toUpperCase()}: ${log.message}${log.data ? ' | Data: ' + JSON.stringify(log.data) : ''}`
        ).join('\n');
        
        console.log('📋 Exported logs:\n', logsText);
        return logsText;
    },
    
    clearLogs() {
        this.logs = [];
        this.log('Logs cleared', 'info');
    }
};

// Auto-start diagnostic when loaded
window.ReinoDebug.log('Debug helper initialized', 'success');

// Listen for ReinoCalculator ready event
document.addEventListener('reinoCalculatorReady', (event) => {
    window.ReinoDebug.log('reinoCalculatorReady event received', 'success', event.detail);
    window.ReinoDebug.runFullDiagnostic();
});

// Fallback diagnostic after 3 seconds
setTimeout(() => {
    if (!window.ReinoCalculator) {
        window.ReinoDebug.log('ReinoCalculator not loaded after 3 seconds, running diagnostic anyway', 'warning');
        window.ReinoDebug.runFullDiagnostic();
    }
}, 3000);

// Make it globally available
window.debugReino = window.ReinoDebug;

export default window.ReinoDebug;
