# 🚀 Reino Calculator - CDN Setup Guide

## 🔄 **Atualização Automática**

Este projeto agora possui **deploy automático**! Sempre que você fizer push na branch `master`, os arquivos do CDN são atualizados automaticamente.

## 📦 Arquivos Disponíveis via jsDelivr

### Bundle Completo

```html
<!-- V<PERSON><PERSON> mais recente (v1.0.2) - RECOMENDADO -->
<script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v1.0.2/dist/index.js"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v1.0.2/dist/index.css">

<!-- Fallback para v1.0.1 (se v1.0.2 não estiver disponível) -->
<script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v1.0.1/dist/index.js"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v1.0.1/dist/index.css">

<!-- Ou use sempre a última versão (pode ter cache) -->
<script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/index.js"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/index.css">
```

### Módulos Individuais (Todos os 19 módulos disponíveis)

```html
<!-- Sistema de Seleção de Ativos -->
<script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/modules/asset-selection-filter.js"></script>

<!-- Animação de Tags de Ativos -->
<script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/modules/ativo-color-tag-animation.js"></script>

<!-- Corretor de Atributos -->
<script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/modules/attribute-fixer.js"></script>

<!-- Controle de Moeda -->
<script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/modules/currency-control.js"></script>

<!-- Formatação de Moeda -->
<script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/modules/currency-formatting.js"></script>

<!-- Integração DGM Canvas -->
<script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/modules/dgm-canvas-integration.js"></script>

<!-- Coordenador de Eventos -->
<script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/modules/event-coordinator.js"></script>

<!-- Animações Motion -->
<script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/modules/motion-animation.js"></script>

<!-- Sincronização Patrimonial -->
<script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/modules/patrimony-sync.js"></script>

<!-- Sistema de Produtos -->
<script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/modules/product-system.js"></script>

<!-- Sistema de Navegação por Passos -->
<script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/modules/progress-bar-system.js"></script>

<!-- Sincronização de Resultados -->
<script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/modules/resultado-sync.js"></script>

<!-- Integração Salesforce -->
<script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/modules/salesforce-integration.js"></script>

<!-- Sincronização Salesforce -->
<script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/modules/salesforce-sync.js"></script>

<!-- Animação de Scroll Float -->
<script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/modules/scroll-float-animation.js"></script>

<!-- Visibilidade de Seções -->
<script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/modules/section-visibility.js"></script>

<!-- Sincronização Simples -->
<script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/modules/simple-sync.js"></script>

<!-- Integração Typebot -->
<script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/modules/typebot-integration.js"></script>

<!-- Integração Webflow Button -->
<script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/modules/webflow-button-integration.js"></script>
```

### CSS

```html
<!-- Estilos para Seleção de Ativos -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/styles/asset-selection-filter.css">
```

## 🔧 Como Usar

### 1. Bundle Completo (Recomendado)

```html
<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/index.css">
</head>
<body>
    <!-- Seu conteúdo Webflow aqui -->

    <script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/index.js"></script>
</body>
</html>
```

### 2. Módulos Específicos

```html
<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/styles/asset-selection-filter.css">
</head>
<body>
    <!-- Seu conteúdo Webflow aqui -->

    <!-- Carregar apenas os módulos necessários -->
    <script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/modules/asset-selection-filter.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/modules/patrimony-sync.js"></script>
</body>
</html>
```

## 🏷️ Versionamento

### Usar Versão Específica (Recomendado para Produção)

```html
<script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v1.0.0/dist/index.js"></script>
```

### Usar Última Versão (Para Desenvolvimento)

```html
<script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/index.js"></script>
```

## 📝 Próximos Passos

1. ✅ **Username configurado:** `joaolucaswork`
2. **Faça commit** dos arquivos dist/
3. **Crie uma release** no GitHub
4. **Teste os links** do jsDelivr

## 🔍 Debug e Troubleshooting

### 🧪 Página de Teste

Use a página de teste para verificar se tudo está funcionando:

```
https://github.com/joaolucaswork/app-calc-reino/blob/master/test-functionality.html
```

### 🔧 Debug via Console

Após carregar, você pode acessar:

```javascript
// API global disponível
window.ReinoCalculator.debugSync(); // Debug completo
window.ReinoCalculator.systems; // Acesso aos sistemas

// Debug helper (v1.0.2+)
window.debugReino.runFullDiagnostic(); // Diagnóstico completo
window.debugReino.testAssetSelection(); // Testar checkboxes
window.debugReino.exportLogs(); // Exportar logs
```

### ⚠️ Problemas Comuns

**Checkboxes não aparecem:**

```javascript
// Verificar se o sistema está inicializado
window.ReinoCalculator.systems.assetSelectionFilter.isInitialized

// Forçar reinicialização
window.ReinoCalculator.systems.assetSelectionFilter.init()
```

**Funcionalidades não funcionam:**

```javascript
// Verificar se todos os sistemas carregaram
window.debugReino.checkSystemInitialization()

// Verificar elementos DOM necessários
window.debugReino.checkRequiredElements()
```
