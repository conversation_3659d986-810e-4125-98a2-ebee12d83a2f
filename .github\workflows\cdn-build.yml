name: Build and Deploy to CDN

on:
  push:
    branches:
      - master
      - main
    tags:
      - 'v*'
  workflow_dispatch:

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: latest

      - name: Install dependencies
        run: pnpm install

      - name: Build for production
        run: pnpm build

      - name: Commit and push dist files
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add dist/
          git diff --staged --quiet || git commit -m "chore: update dist files for CDN [skip ci]"
          git push

      - name: Create or update latest tag
        if: github.ref == 'refs/heads/master' || github.ref == 'refs/heads/main'
        run: |
          git tag -f latest
          git push origin latest --force

      - name: Create Release for tags
        uses: softprops/action-gh-release@v1
        if: startsWith(github.ref, 'refs/tags/') && github.ref != 'refs/tags/latest'
        with:
          files: |
            dist/**/*
          generate_release_notes: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Trigger jsDelivr cache purge
        run: |
          echo "🔄 Triggering jsDelivr cache purge..."
          curl -X POST "https://purge.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/index.js" || true
          curl -X POST "https://purge.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/index.css" || true
          echo "✅ Cache purge triggered"

      - name: Test CDN URLs
        run: |
          echo "🧪 Testing CDN URLs..."
          sleep 10  # Wait for cache purge

          # Test main files
          curl -f "https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/index.js" > /dev/null && echo "✅ index.js OK" || echo "❌ index.js FAILED"
          curl -f "https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/index.css" > /dev/null && echo "✅ index.css OK" || echo "❌ index.css FAILED"

          echo "🎉 CDN deployment completed!"
