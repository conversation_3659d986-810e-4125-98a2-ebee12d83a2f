# Logs
logs
*.log
npm-debug.log*

# Dependency directories
node_modules/

# TypeScript v1 declaration files
typings/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# dotenv environment variables file
.env

# Production files
# dist - Comentado para permitir commit para jsDelivr via GitHub
# Descomente esta linha quando usar npm + GitHub Actions

# Webflow Template Folder
# NOTE: "Modelo - Webflow" folder is tracked but READ-ONLY
# See WEBFLOW_POLICY.md for complete policy details
# Only modify through Webflow platform exports - never edit directly
