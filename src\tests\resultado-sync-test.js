/**
 * Teste de Sincronização da Seção Resultado
 * Verifica se todos os valores são sincronizados corretamente
 */

export class ResultadoSyncTest {
  constructor() {
    this.testResults = [];
    this.isRunning = false;
  }

  async runAllTests() {
    if (this.isRunning) {
      console.warn('⚠️ Testes já estão em execução');
      return;
    }

    this.isRunning = true;
    this.testResults = [];
    
    console.warn('🧪 Iniciando testes de sincronização da seção resultado...');

    try {
      // Aguarda o sistema estar pronto
      await this.waitForSystem();

      // Executa testes individuais
      await this.testModuleInitialization();
      await this.testElementCaching();
      await this.testPatrimonioSync();
      await this.testComissaoSync();
      await this.testVisibilityControl();
      await this.testEventListeners();

      // Mostra resultados
      this.showResults();

    } catch (error) {
      console.error('❌ Erro durante os testes:', error);
    } finally {
      this.isRunning = false;
    }
  }

  async waitForSystem() {
    return new Promise((resolve) => {
      const checkSystem = () => {
        const resultadoSync = window.AppCalcReino?.getModule?.('resultadoSync');
        if (resultadoSync && resultadoSync.isInitialized) {
          resolve();
        } else {
          setTimeout(checkSystem, 100);
        }
      };
      checkSystem();
    });
  }

  async testModuleInitialization() {
    console.warn('🔍 Testando inicialização do módulo...');
    
    try {
      const resultadoSync = window.AppCalcReino?.getModule?.('resultadoSync');
      
      this.assert(resultadoSync, 'Módulo ResultadoSync deve estar disponível');
      this.assert(resultadoSync.isInitialized, 'Módulo deve estar inicializado');
      
      this.testResults.push({
        test: 'Module Initialization',
        status: 'PASS',
        message: 'Módulo inicializado corretamente'
      });

    } catch (error) {
      this.testResults.push({
        test: 'Module Initialization',
        status: 'FAIL',
        message: error.message
      });
    }
  }

  async testElementCaching() {
    console.warn('🔍 Testando cache de elementos...');
    
    try {
      const resultadoSync = window.AppCalcReino?.getModule?.('resultadoSync');
      
      this.assert(resultadoSync.patrimonioItems.size > 0, 'Deve ter elementos de patrimônio cacheados');
      this.assert(resultadoSync.resultadoPatrimonioItems.size > 0, 'Deve ter elementos de resultado patrimônio cacheados');
      this.assert(resultadoSync.resultadoComissaoItems.size > 0, 'Deve ter elementos de resultado comissão cacheados');
      
      console.warn(`📊 Elementos cacheados:`, {
        patrimonio: resultadoSync.patrimonioItems.size,
        resultadoPatrimonio: resultadoSync.resultadoPatrimonioItems.size,
        resultadoComissao: resultadoSync.resultadoComissaoItems.size
      });

      this.testResults.push({
        test: 'Element Caching',
        status: 'PASS',
        message: `${resultadoSync.patrimonioItems.size} elementos cacheados`
      });

    } catch (error) {
      this.testResults.push({
        test: 'Element Caching',
        status: 'FAIL',
        message: error.message
      });
    }
  }

  async testPatrimonioSync() {
    console.warn('🔍 Testando sincronização de patrimônio...');
    
    try {
      const resultadoSync = window.AppCalcReino?.getModule?.('resultadoSync');
      
      // Testa se existe pelo menos um item para testar
      const firstKey = resultadoSync.patrimonioItems.keys().next().value;
      if (!firstKey) {
        throw new Error('Nenhum item de patrimônio encontrado para teste');
      }

      const patrimonioItem = resultadoSync.patrimonioItems.get(firstKey);
      const resultadoItem = resultadoSync.resultadoPatrimonioItems.get(firstKey);

      this.assert(patrimonioItem, 'Item de patrimônio deve existir');
      this.assert(resultadoItem, 'Item de resultado deve existir');
      this.assert(patrimonioItem.input, 'Input de patrimônio deve existir');
      this.assert(resultadoItem.valorElement, 'Elemento de valor no resultado deve existir');

      // Simula mudança de valor
      const testValue = 50000;
      if (patrimonioItem.input) {
        patrimonioItem.input.value = testValue.toLocaleString('pt-BR');
        patrimonioItem.input.dispatchEvent(new Event('input'));
        
        // Aguarda sincronização
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Verifica se o valor foi sincronizado
        const displayedValue = resultadoItem.valorElement.textContent;
        this.assert(displayedValue.includes('50.000'), `Valor deve ser sincronizado (atual: ${displayedValue})`);
      }

      this.testResults.push({
        test: 'Patrimonio Sync',
        status: 'PASS',
        message: 'Sincronização de patrimônio funcionando'
      });

    } catch (error) {
      this.testResults.push({
        test: 'Patrimonio Sync',
        status: 'FAIL',
        message: error.message
      });
    }
  }

  async testComissaoSync() {
    console.warn('🔍 Testando sincronização de comissões...');
    
    try {
      const resultadoSync = window.AppCalcReino?.getModule?.('resultadoSync');
      
      // Testa se existe pelo menos um item para testar
      const firstKey = resultadoSync.resultadoComissaoItems.keys().next().value;
      if (!firstKey) {
        throw new Error('Nenhum item de comissão encontrado para teste');
      }

      const comissaoItem = resultadoSync.resultadoComissaoItems.get(firstKey);
      
      this.assert(comissaoItem, 'Item de comissão deve existir');
      this.assert(comissaoItem.taxaMinimaElement, 'Elemento de taxa mínima deve existir');
      this.assert(comissaoItem.taxaMaximaElement, 'Elemento de taxa máxima deve existir');

      // Verifica se as taxas foram definidas
      const taxaMinima = comissaoItem.taxaMinimaElement.textContent;
      const taxaMaxima = comissaoItem.taxaMaximaElement.textContent;
      
      this.assert(taxaMinima.includes('%'), 'Taxa mínima deve conter %');
      this.assert(taxaMaxima.includes('%'), 'Taxa máxima deve conter %');

      this.testResults.push({
        test: 'Comissao Sync',
        status: 'PASS',
        message: 'Sincronização de comissões funcionando'
      });

    } catch (error) {
      this.testResults.push({
        test: 'Comissao Sync',
        status: 'FAIL',
        message: error.message
      });
    }
  }

  async testVisibilityControl() {
    console.warn('🔍 Testando controle de visibilidade...');
    
    try {
      const resultadoSync = window.AppCalcReino?.getModule?.('resultadoSync');
      
      // Testa visibilidade inicial (todos visíveis)
      let visiblePatrimonioItems = 0;
      let visibleComissaoItems = 0;

      resultadoSync.resultadoPatrimonioItems.forEach((item) => {
        if (item.element.style.display !== 'none') {
          visiblePatrimonioItems++;
        }
      });

      resultadoSync.resultadoComissaoItems.forEach((item) => {
        if (item.element.style.display !== 'none') {
          visibleComissaoItems++;
        }
      });

      this.assert(visiblePatrimonioItems > 0, 'Deve ter itens de patrimônio visíveis');
      this.assert(visibleComissaoItems > 0, 'Deve ter itens de comissão visíveis');

      this.testResults.push({
        test: 'Visibility Control',
        status: 'PASS',
        message: `${visiblePatrimonioItems} patrimônio, ${visibleComissaoItems} comissão visíveis`
      });

    } catch (error) {
      this.testResults.push({
        test: 'Visibility Control',
        status: 'FAIL',
        message: error.message
      });
    }
  }

  async testEventListeners() {
    console.warn('🔍 Testando event listeners...');
    
    try {
      const resultadoSync = window.AppCalcReino?.getModule?.('resultadoSync');
      
      // Testa se o módulo responde a eventos
      let eventReceived = false;
      
      const originalForceSync = resultadoSync.forceSync;
      resultadoSync.forceSync = () => {
        eventReceived = true;
        originalForceSync.call(resultadoSync);
      };

      // Dispara evento de teste
      document.dispatchEvent(new CustomEvent('resultadoSyncTest'));
      
      // Chama forceSync para testar
      resultadoSync.forceSync();
      
      this.assert(eventReceived, 'Módulo deve responder a chamadas de método');

      // Restaura método original
      resultadoSync.forceSync = originalForceSync;

      this.testResults.push({
        test: 'Event Listeners',
        status: 'PASS',
        message: 'Event listeners funcionando'
      });

    } catch (error) {
      this.testResults.push({
        test: 'Event Listeners',
        status: 'FAIL',
        message: error.message
      });
    }
  }

  assert(condition, message) {
    if (!condition) {
      throw new Error(message);
    }
  }

  showResults() {
    console.warn('📊 Resultados dos testes:');
    
    const passed = this.testResults.filter(r => r.status === 'PASS').length;
    const failed = this.testResults.filter(r => r.status === 'FAIL').length;
    
    console.table(this.testResults);
    
    if (failed === 0) {
      console.warn(`✅ Todos os ${passed} testes passaram!`);
    } else {
      console.warn(`⚠️ ${passed} testes passaram, ${failed} falharam`);
    }

    // Cria relatório visual no DOM
    this.createVisualReport();
  }

  createVisualReport() {
    // Remove relatório anterior se existir
    const existingReport = document.getElementById('resultado-sync-test-report');
    if (existingReport) {
      existingReport.remove();
    }

    // Cria novo relatório
    const report = document.createElement('div');
    report.id = 'resultado-sync-test-report';
    report.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: white;
      border: 2px solid #333;
      border-radius: 8px;
      padding: 16px;
      max-width: 400px;
      z-index: 10000;
      font-family: monospace;
      font-size: 12px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    `;

    const passed = this.testResults.filter(r => r.status === 'PASS').length;
    const failed = this.testResults.filter(r => r.status === 'FAIL').length;

    report.innerHTML = `
      <h3 style="margin: 0 0 12px 0; color: #333;">🧪 Resultado Sync Tests</h3>
      <div style="margin-bottom: 12px;">
        <span style="color: green;">✅ ${passed} passed</span> | 
        <span style="color: red;">❌ ${failed} failed</span>
      </div>
      ${this.testResults.map(result => `
        <div style="margin: 4px 0; padding: 4px; background: ${result.status === 'PASS' ? '#e8f5e8' : '#ffe8e8'};">
          <strong>${result.test}:</strong> ${result.status}<br>
          <small>${result.message}</small>
        </div>
      `).join('')}
      <button onclick="this.parentElement.remove()" style="margin-top: 12px; padding: 4px 8px;">Close</button>
    `;

    document.body.appendChild(report);

    // Remove automaticamente após 30 segundos
    setTimeout(() => {
      if (report.parentElement) {
        report.remove();
      }
    }, 30000);
  }
}

// Expor globalmente para facilitar testes manuais
if (typeof window !== 'undefined') {
  window.ResultadoSyncTest = ResultadoSyncTest;
  
  // Função de conveniência para executar testes
  window.testResultadoSync = async () => {
    const test = new ResultadoSyncTest();
    await test.runAllTests();
  };
}
