# 🚀 Guia Completo de Deploy para jsDelivr

## ✅ Pré-requisitos Concluídos

- [x] Package.json configurado
- [x] Build configurado para múltiplos entry points
- [x] Arquivos de produção gerados em `dist/`
- [x] GitHub Actions configurado
- [x] Documentação CDN criada

## 📋 Passos para Produção

### 1. **Configurar Repositório GitHub**

#### 1.1 Criar repositório no GitHub

```bash
# Se ainda não existe, crie um repositório público no GitHub
# Nome sugerido: app-calc-reino
```

#### 1.2 ✅ URLs Atualizadas

Username configurado: `joaolucaswork`

- ✅ `package.json` atualizado
- ✅ `CDN-SETUP.md` atualizado

#### 1.3 Conectar repositório local

```bash
# Se ainda não conectado
git remote add origin https://github.com/joaolucaswork/app-calc-reino.git
git branch -M main
git push -u origin main
```

### 2. **Criar Primeira Release**

#### 2.1 Criar tag de versão

```bash
git tag v1.0.0
git push origin v1.0.0
```

#### 2.2 Criar release no GitHub

1. Vá para: `https://github.com/SEU_USUARIO/app-calc-reino/releases`
2. Clique em "Create a new release"
3. Tag: `v1.0.0`
4. Title: `v1.0.0 - Primeira versão para CDN`
5. Descrição:

```markdown
## 🚀 Reino Calculator v1.0.0

### Módulos Disponíveis via CDN
- Bundle completo com todos os sistemas
- Módulos individuais para uso específico
- CSS otimizado

### Como usar
```html
<script src="https://cdn.jsdelivr.net/gh/SEU_USUARIO/app-calc-reino@v1.0.0/dist/index.js"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/SEU_USUARIO/app-calc-reino@v1.0.0/dist/index.css">
```

Ver documentação completa em CDN-SETUP.md

```
6. Clique em "Publish release"

### 3. **Testar jsDelivr**

#### 3.1 URLs para testar:
```

<https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v1.0.0/dist/index.js>
<https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v1.0.0/dist/index.css>

```

#### 3.2 Teste em HTML:
```html
<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v1.0.0/dist/index.css">
</head>
<body>
    <h1>Teste Reino Calculator</h1>
    <script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v1.0.0/dist/index.js"></script>
    <script>
        // Verificar se carregou
        setTimeout(() => {
            console.log('Reino Calculator:', window.ReinoCalculator);
        }, 1000);
    </script>
</body>
</html>
```

### 4. **Uso em Produção**

#### 4.1 No Webflow

1. Vá para Project Settings > Custom Code
2. Adicione no `<head>`:

```html
<link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v1.0.0/dist/index.css">
```

3. Adicione antes do `</body>`:

```html
<script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v1.0.0/dist/index.js"></script>
```

#### 4.2 Versões Minificadas (Automáticas)

```html
<!-- jsDelivr minifica automaticamente -->
<script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v1.0.0/dist/index.min.js"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v1.0.0/dist/index.min.css">
```

### 5. **Atualizações Futuras**

#### 5.1 Para nova versão

```bash
# Fazer alterações no código
pnpm build
git add .
git commit -m "feat: nova funcionalidade"
git tag v1.1.0
git push origin main
git push origin v1.1.0
```

#### 5.2 Criar nova release no GitHub

- Repita o processo do passo 2.2 com nova versão

### 6. **Opção Avançada: npm + jsDelivr**

#### 6.1 Publicar no npm (opcional)

```bash
npm login
npm publish
```

#### 6.2 Usar via npm no jsDelivr

```html
<script src="https://cdn.jsdelivr.net/npm/app-calc-reino@1.0.0/dist/index.js"></script>
```

## 🎯 URLs Finais (Substitua SEU_USUARIO)

### Bundle Completo

```
https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v1.0.0/dist/index.js
https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v1.0.0/dist/index.css
```

### Módulos Individuais

```
https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v1.0.0/dist/modules/asset-selection-filter.js
https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v1.0.0/dist/modules/patrimony-sync.js
https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v1.0.0/dist/modules/currency-control.js
```

## ✅ Checklist Final

- [x] ✅ URLs configuradas para `joaolucaswork`
- [ ] Criar repositório público no GitHub
- [ ] Fazer push do código
- [ ] Criar primeira release v1.0.0
- [ ] Testar URLs do jsDelivr
- [ ] Implementar no Webflow
- [ ] Testar funcionamento completo
