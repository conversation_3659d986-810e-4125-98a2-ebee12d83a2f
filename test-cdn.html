<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Reino Calculator CDN</title>
    
    <!-- CSS do Reino Calculator via jsDelivr -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v1.0.0/dist/index.css">
    
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .loading { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        
        .test-urls {
            margin: 20px 0;
        }
        
        .test-urls a {
            display: block;
            margin: 5px 0;
            color: #007bff;
            text-decoration: none;
        }
        
        .test-urls a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🚀 Teste Reino Calculator CDN</h1>
        
        <div id="loading-status" class="status loading">
            ⏳ Carregando Reino Calculator...
        </div>
        
        <div id="success-status" class="status success" style="display: none;">
            ✅ Reino Calculator carregado com sucesso!
        </div>
        
        <div id="error-status" class="status error" style="display: none;">
            ❌ Erro ao carregar Reino Calculator
        </div>
        
        <div class="status info">
            <h3>📋 URLs de Teste:</h3>
            <div class="test-urls">
                <strong>JavaScript:</strong>
                <a href="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v1.0.0/dist/index.js" target="_blank">
                    https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v1.0.0/dist/index.js
                </a>
                
                <strong>CSS:</strong>
                <a href="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v1.0.0/dist/index.css" target="_blank">
                    https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v1.0.0/dist/index.css
                </a>
                
                <strong>Módulos Individuais:</strong>
                <a href="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v1.0.0/dist/modules/asset-selection-filter.js" target="_blank">
                    Asset Selection Filter
                </a>
                <a href="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v1.0.0/dist/modules/patrimony-sync.js" target="_blank">
                    Patrimony Sync
                </a>
                <a href="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v1.0.0/dist/modules/currency-control.js" target="_blank">
                    Currency Control
                </a>
            </div>
        </div>
        
        <div id="debug-info" style="display: none;">
            <h3>🔍 Informações de Debug:</h3>
            <pre id="debug-content"></pre>
        </div>
    </div>

    <!-- JavaScript do Reino Calculator via jsDelivr -->
    <script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v1.0.0/dist/index.js"></script>
    
    <script>
        // Teste de carregamento
        let loadTimeout;
        
        function showSuccess() {
            document.getElementById('loading-status').style.display = 'none';
            document.getElementById('success-status').style.display = 'block';
            document.getElementById('debug-info').style.display = 'block';
            
            // Mostrar informações de debug
            const debugInfo = {
                'Reino Calculator Disponível': !!window.ReinoCalculator,
                'Sistemas Carregados': window.ReinoCalculator ? Object.keys(window.ReinoCalculator.systems || {}) : [],
                'API Global': window.ReinoCalculator ? Object.keys(window.ReinoCalculator) : [],
                'Timestamp': new Date().toISOString()
            };
            
            document.getElementById('debug-content').textContent = JSON.stringify(debugInfo, null, 2);
            
            clearTimeout(loadTimeout);
        }
        
        function showError() {
            document.getElementById('loading-status').style.display = 'none';
            document.getElementById('error-status').style.display = 'block';
        }
        
        // Verificar se carregou após 3 segundos
        loadTimeout = setTimeout(() => {
            if (window.ReinoCalculator) {
                showSuccess();
            } else {
                showError();
            }
        }, 3000);
        
        // Verificar imediatamente se já carregou
        if (window.ReinoCalculator) {
            showSuccess();
        }
        
        // Escutar evento de carregamento
        document.addEventListener('reinoCalculatorReady', function(event) {
            console.log('Reino Calculator Ready Event:', event.detail);
            showSuccess();
        });
        
        // Log de debug
        console.log('🚀 Teste Reino Calculator CDN iniciado');
        console.log('Aguardando carregamento...');
    </script>
</body>
</html>
