name: Auto Version and Release

on:
  workflow_dispatch:
    inputs:
      version_type:
        description: 'Version bump type'
        required: true
        default: 'patch'
        type: choice
        options:
          - patch
          - minor
          - major

jobs:
  version-and-release:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: latest

      - name: Install dependencies
        run: pnpm install

      - name: Build for production
        run: pnpm build

      - name: Bump version
        id: version
        run: |
          # Get current version
          CURRENT_VERSION=$(node -p "require('./package.json').version")
          echo "Current version: $CURRENT_VERSION"

          # Bump version
          npm version ${{ github.event.inputs.version_type }} --no-git-tag-version

          # Get new version
          NEW_VERSION=$(node -p "require('./package.json').version")
          echo "New version: $NEW_VERSION"
          echo "new_version=$NEW_VERSION" >> $GITHUB_OUTPUT

      - name: Commit changes
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add package.json dist/
          git commit -m "chore: bump version to v${{ steps.version.outputs.new_version }}"
          git push

      - name: Create and push tag
        run: |
          git tag v${{ steps.version.outputs.new_version }}
          git push origin v${{ steps.version.outputs.new_version }}

      - name: Create GitHub Release
        uses: softprops/action-gh-release@v1
        with:
          tag_name: v${{ steps.version.outputs.new_version }}
          name: Reino Calculator v${{ steps.version.outputs.new_version }}
          body: |
            ## 🚀 Reino Calculator v${{ steps.version.outputs.new_version }}

            ### 📦 CDN URLs
            ```html
            <!-- Bundle Completo -->
            <script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v${{ steps.version.outputs.new_version }}/dist/index.js"></script>
            <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v${{ steps.version.outputs.new_version }}/dist/index.css">

            <!-- Ou use a versão latest -->
            <script src="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/index.js"></script>
            <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/index.css">
            ```

            ### 🔗 Links Úteis
            - [Documentação CDN](https://github.com/joaolucaswork/app-calc-reino/blob/master/CDN-SETUP.md)
            - [Guia de Deploy](https://github.com/joaolucaswork/app-calc-reino/blob/master/DEPLOY-GUIDE.md)
          files: |
            dist/**/*
          generate_release_notes: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Trigger jsDelivr cache purge
        run: |
          echo "🔄 Purging jsDelivr cache for new version..."
          curl -X POST "https://purge.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v${{ steps.version.outputs.new_version }}/dist/index.js" || true
          curl -X POST "https://purge.jsdelivr.net/gh/joaolucaswork/app-calc-reino@v${{ steps.version.outputs.new_version }}/dist/index.css" || true
          curl -X POST "https://purge.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/index.js" || true
          curl -X POST "https://purge.jsdelivr.net/gh/joaolucaswork/app-calc-reino@latest/dist/index.css" || true
          echo "✅ Cache purge completed"
