/**
 * Typebot Integration Configuration
 * Configure your Typebot credentials and settings here
 */

// Typebot Configuration
const TYPEBOT_CONFIG = {
  // Your Typebot public ID (get this from your Typebot URL)
  PUBLIC_ID: 'relatorio-reino', // ✅ Atualizado com ID correto usado no HTML

  // API Base URL (use your self-hosted instance or typebot.io)
  API_BASE_URL: 'https://typebot.co/api/v1', // ✅ Atualizado para typebot.co

  // Authorization token (optional - needed for private bots)
  AUTH_TOKEN: 'Bearer Dza0r0rRB4rzjSeGiKlawQK8', // ✅ Configurado

  // Webhook configuration from your Typebot
  WEBHOOK_CONFIG: {
    blockId: 'o8gkxhaloh3c3w5ldx94zlwx',
    webhookUrl:
      'https://typebot.co/api/v1/typebots/mp0wrmwrxpqnycbxlcimcz5n/blocks/o8gkxhaloh3c3w5ldx94zlwx/results/{resultId}/executeWebhook',
  },

  // Typebot Embed Configuration
  EMBED_CONFIG: {
    // Container where the typebot will be embedded
    containerId: 'typebot-container',

    // Use official Typebot popup
    usePopup: true,

    // CDN URL for Typebot JS library
    cdnUrl: 'https://cdn.jsdelivr.net/npm/@typebot.io/js@0/dist/web.js',

    // Typebot theme customization
    theme: {
      button: {
        backgroundColor: '#0042DA',
        iconColor: '#FFFFFF',
        customIconSrc: null,
        size: 'medium',
        dragAndDrop: false,
        position: 'right',
      },
      chatWindow: {
        backgroundColor: '#FFFFFF',
        maxWidth: '400px',
        maxHeight: '600px',
      },
    },

    // Auto-open configuration
    autoOpenDelay: 0, // Set to 0 to not auto-open, or milliseconds to delay
  },

  // Webhook endpoint for completion callbacks
  COMPLETION_WEBHOOK: '/api/typebot-completion', // Your endpoint to handle completion

  // Debug mode
  DEBUG: window.location.hostname === 'localhost' || window.location.search.includes('debug=true'),
};

// Variable mapping - maps your form data to Typebot variables
const VARIABLE_MAPPING = {
  // Your form field name -> Typebot variable name
  patrimonio: 'patrimonio', // Ajustado para minúsculo
  ativosEscolhidos: 'ativos', // Nome do campo coletado no form
  email: 'email', // Será coletado pelo Typebot
  nome: 'nome', // Será coletado pelo Typebot
  alocacao: 'alocacao', // Dados de alocação
  totalAlocado: 'totalAlocado', // Total alocado calculado
  patrimonioRestante: 'patrimonioRestante', // Patrimônio restante
  sessionId: 'sessionId',
};

// Export configuration
export { TYPEBOT_CONFIG, VARIABLE_MAPPING };

/**
 * Typebot Client for API interactions
 */
export class TypebotClient {
  constructor(config = TYPEBOT_CONFIG) {
    this.config = config;
    this.sessionId = null;
    this.resultId = null;
    this.isCompleted = false;
  }

  /**
   * Start a new Typebot chat session
   * @param {Object} prefilledData - Data to prefill in the typebot
   * @returns {Promise<Object>} - Session data
   */
  async startChat(prefilledData = {}) {
    try {
      // Check if fetch is available
      if (typeof fetch === 'undefined') {
        throw new Error('Fetch is not available in this environment');
      }

      const url = `${this.config.API_BASE_URL}/typebots/${this.config.PUBLIC_ID}/startChat`;

      // Map form data to typebot variables
      const prefilledVariables = this.mapFormDataToVariables(prefilledData);

      const requestBody = {
        prefilledVariables,
        isStreamEnabled: false,
        textBubbleContentFormat: 'richText',
      };

      const headers = {
        'Content-Type': 'application/json',
      };

      // Add authorization if needed
      if (this.config.AUTH_TOKEN) {
        headers['Authorization'] = this.config.AUTH_TOKEN;
      }

      const response = await window.fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`Typebot API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // Store session information
      this.sessionId = data.sessionId;
      this.resultId = data.resultId;

      if (this.config.DEBUG) {
        // console.log('🤖 Typebot chat started:', {
        //   sessionId: this.sessionId,
        //   resultId: this.resultId,
        //   prefilledVariables,
        // });
      }

      return data;
    } catch (error) {
      console.error('❌ Failed to start Typebot chat:', error);
      throw error;
    }
  }

  /**
   * Continue the chat with a message
   * @param {string} message - Message to send
   * @returns {Promise<Object>} - Response data
   */
  async continueChat(message) {
    if (!this.sessionId) {
      throw new Error('No active session. Start chat first.');
    }

    try {
      const url = `${this.config.API_BASE_URL}/sessions/${this.sessionId}/continueChat`;

      const requestBody = {
        message: {
          type: 'text',
          text: message,
        },
        textBubbleContentFormat: 'richText',
      };

      const headers = {
        'Content-Type': 'application/json',
      };

      if (this.config.AUTH_TOKEN) {
        headers['Authorization'] = this.config.AUTH_TOKEN;
      }

      const response = await window.fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(`Typebot API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (this.config.DEBUG) {
        // console.log('🤖 Typebot chat continued:', data);
      }

      return data;
    } catch (error) {
      console.error('❌ Failed to continue Typebot chat:', error);
      throw error;
    }
  }

  /**
   * Map form data to typebot variables
   * @param {Object} formData - Form data from your application
   * @returns {Object} - Mapped variables for typebot
   */
  mapFormDataToVariables(formData) {
    const mappedVariables = {};

    // Calculate derived values
    const totalAlocado = formData.alocacao
      ? Object.values(formData.alocacao).reduce((sum, item) => sum + (item.value || 0), 0)
      : 0;
    const patrimonioRestante = (formData.patrimonio || 0) - totalAlocado;

    // Add calculated values to formData for mapping
    const enhancedFormData = {
      ...formData,
      totalAlocado,
      patrimonioRestante,
    };

    for (const [formField, typebotVar] of Object.entries(VARIABLE_MAPPING)) {
      if (enhancedFormData[formField] !== undefined) {
        // Handle special formatting for certain fields
        if (formField === 'patrimonio' && typeof enhancedFormData[formField] === 'number') {
          mappedVariables[typebotVar] = `R$ ${enhancedFormData[formField].toLocaleString('pt-BR')}`;
        } else if (
          formField === 'totalAlocado' &&
          typeof enhancedFormData[formField] === 'number'
        ) {
          mappedVariables[typebotVar] = `R$ ${enhancedFormData[formField].toLocaleString('pt-BR')}`;
        } else if (
          formField === 'patrimonioRestante' &&
          typeof enhancedFormData[formField] === 'number'
        ) {
          mappedVariables[typebotVar] = `R$ ${enhancedFormData[formField].toLocaleString('pt-BR')}`;
        } else if (formField === 'ativosEscolhidos' && Array.isArray(enhancedFormData[formField])) {
          // Format selected assets as readable text
          const ativosList = enhancedFormData[formField]
            .map((ativo) => `${ativo.product} (${ativo.category})`)
            .join(', ');
          mappedVariables[typebotVar] = ativosList;
        } else if (formField === 'alocacao' && typeof enhancedFormData[formField] === 'object') {
          // Format allocation data as readable text, only showing non-zero values
          const alocacaoList = Object.entries(enhancedFormData[formField])
            .filter(([, data]) => (data.value || 0) > 0)
            .map(
              ([, data]) =>
                `${data.product}: R$ ${(data.value || 0).toLocaleString('pt-BR')} (${Math.round((data.percentage || 0) * 100) / 100}%)`
            )
            .join('\n');
          mappedVariables[typebotVar] = alocacaoList || 'Nenhuma alocação realizada';
        } else {
          mappedVariables[typebotVar] = String(enhancedFormData[formField]);
        }
      }
    }

    return mappedVariables;
  }

  /**
   * Check if the chat is completed
   * @returns {boolean}
   */
  isSessionCompleted() {
    return this.isCompleted;
  }

  /**
   * Mark session as completed
   */
  markCompleted() {
    this.isCompleted = true;

    if (this.config.DEBUG) {
      // console.log('🤖 Typebot session marked as completed');
    }
  }

  /**
   * Reset the client for a new session
   */
  reset() {
    this.sessionId = null;
    this.resultId = null;
    this.isCompleted = false;
  }
}
