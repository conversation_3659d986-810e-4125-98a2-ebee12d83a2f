# Reino Calculator App - AI Assistant Instructions

## Project Overview

This is a **modular financial calculator** for Grupo Reino, built with **TypeScript/JavaScript ES modules** and integrated with **Webflow templates**. The app calculates asset allocation and patrimony values with multiple third-party integrations.

### Critical Architectural Principle

**🚨 WEBFLOW READ-ONLY POLICY**: The `Modelo - Webflow/` folder is **STRICTLY READ-ONLY**. Never edit files directly in this folder. Changes must be made in Webflow platform first, then exported. Use `pnpm validate:webflow` to check compliance.

## Core Architecture

### Modular System Design

The app uses a **centralized coordinator pattern** in `src/app.js` that initializes systems in a specific order:

```javascript
// Initialization order is critical:
'attributeFixer',         // Fix inconsistencies FIRST
'assetSelectionFilter',   // Setup checkboxes EARLY
'currencyFormatting',     // Base currency system
'patrimonySync',          // BEFORE currency controls
'currencyControl',        // AFTER patrimony sync
// ... more systems
```

Each module in `src/modules/` is a self-contained ES6 class with `.init()` method. Systems communicate via `event-coordinator.js` using custom events.

### Key Entry Points

- `src/index.ts` - Main entry point, imports `app.js` and CSS
- `src/app.js` - ReinoCalculatorApp class coordinates all systems
- `bin/build.js` - esbuild configuration with dev server on port 3000

## Development Workflow

### Local Development

```bash
pnpm dev     # Starts dev server on localhost:3000 with live reload
pnpm build   # Production build to dist/
pnpm test    # Playwright tests (tests Webflow HTML directly)
```

### Testing Strategy

Tests use **Playwright** to test against actual Webflow HTML files in `Modelo - Webflow/index.html`. Tests navigate through sections and verify module interactions. Example pattern:

```typescript
// Load local Webflow file
await page.goto('file:///path/to/Modelo - Webflow/index.html');
// Test actual DOM elements with Webflow classes
const assetsSection = page.locator('._2-section-calc-ativos');
```

## External Integrations

### Typebot Integration

- Config: `src/config/typebot.js` with `PUBLIC_ID` and `API_BASE_URL`
- Flow: Form submission → Typebot conversation → Auto-send to Supabase
- Module: `typebot-integration.js` handles popup and data passing

### Salesforce Integration

- Config: `src/config/salesforce.js`
- Module: `salesforce-integration.js` syncs form data bidirectionally
- Database: `database/salesforce-sync-migration.sql` for field mapping

### Supabase Backend

- Config: `src/config/supabase.js`
- Database setup: `database/supabase-setup.sql`
- Handles form submissions and data persistence

## Project-Specific Patterns

### Asset Selection System

The app implements a **two-section selection pattern**:

- Section 2: Asset checkboxes with `ativo-category` and `ativo-product` attributes
- Section 3: Shows only selected assets filtered dynamically
- Module: `asset-selection-filter.js` with public API methods

### Currency Control Pattern

**Two-layer currency system**:

1. `currency-formatting.js` - Base formatting with Currency.js
2. `currency-control.js` - +/- buttons with smart increment logic
3. `patrimony-sync.js` - Syncs main input with allocations

### Webflow Integration Conventions

- Uses `element-function` attributes for JS targeting
- Classes follow Webflow naming: `._2-section-calc-ativos`
- Button integration via `webflow-button-integration.js`
- Step navigation uses `data-step` attributes

## Common Tasks

### Adding New Modules

1. Create class in `src/modules/new-module.js` with `init()` method
2. Add to `systems` object in `app.js`
3. Add to `initializationOrder` array at correct position
4. Use `eventCoordinator` for cross-module communication

### Webflow Template Changes

1. **DO NOT** edit files in `Modelo - Webflow/` directly
2. Make changes in Webflow platform
3. Export and replace entire folder
4. Run `pnpm validate:webflow` to verify compliance

### Debugging

- Development builds include sourcemaps and live reload
- Global API available: `window.ReinoCalculator.systems`
- Console logs are conditional based on environment detection
