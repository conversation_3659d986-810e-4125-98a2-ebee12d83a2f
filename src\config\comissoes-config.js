/**
 * Configuração de Comissões
 * Define as taxas mínimas e máximas para cada tipo de ativo
 * Baseado em dados reais do mercado financeiro brasileiro
 */

export const COMISSOES_CONFIG = {
  'Renda Fixa': {
    'CDB': { 
      min: 0.5, 
      max: 2.0,
      description: 'Certificado de Depósito Bancário',
      risco: 'Baixo'
    },
    'CRI': { 
      min: 0.8, 
      max: 2.5,
      description: 'Certificado de Recebíveis Imobiliários',
      risco: '<PERSON>é<PERSON>'
    },
    'Tí<PERSON>los Públicos': { 
      min: 0.3, 
      max: 1.5,
      description: '<PERSON><PERSON>uro Direto',
      risco: '<PERSON><PERSON>'
    }
  },
  'Fundo de investimento': {
    'Ações': { 
      min: 1.5, 
      max: 3.0,
      description: 'Fundos de Ações',
      risco: 'Alto'
    },
    'Liquidez': { 
      min: 0.3, 
      max: 1.0,
      description: 'Fundos de Liquidez',
      risco: 'Baixo'
    },
    'Renda Fixa': { 
      min: 0.8, 
      max: 2.0,
      description: 'Fundos de Renda Fixa',
      risco: 'Baixo'
    }
  },
  'Renda variável': {
    'Ações': { 
      min: 2.0, 
      max: 4.0,
      description: 'Ações Individuais',
      risco: 'Alto'
    },
    'Estruturada': { 
      min: 1.0, 
      max: 3.5,
      description: 'Produtos Estruturados',
      risco: 'Alto'
    },
    'Carteira administrada': { 
      min: 1.5, 
      max: 3.0,
      description: 'Carteira Administrada',
      risco: 'Médio-Alto'
    }
  },
  'Outros': {
    'Poupança': { 
      min: 0.0, 
      max: 0.0,
      description: 'Caderneta de Poupança',
      risco: 'Muito Baixo'
    },
    'Previdência': { 
      min: 1.0, 
      max: 2.5,
      description: 'Previdência Privada',
      risco: 'Variável'
    },
    'Imóvel': { 
      min: 3.0, 
      max: 6.0,
      description: 'Investimento Imobiliário',
      risco: 'Médio'
    },
    'COE': { 
      min: 0.5, 
      max: 2.0,
      description: 'Certificado de Operações Estruturadas',
      risco: 'Médio'
    },
    'Operação compromissada': { 
      min: 0.3, 
      max: 1.2,
      description: 'Operação Compromissada',
      risco: 'Baixo'
    },
    'Criptoativos': { 
      min: 0.5, 
      max: 2.0,
      description: 'Criptomoedas e Tokens',
      risco: 'Muito Alto'
    }
  }
};

/**
 * Utilitários para trabalhar com configuração de comissões
 */
export class ComissoesUtils {
  /**
   * Obtém dados de comissão para uma categoria e produto específicos
   * @param {string} category - Categoria do ativo
   * @param {string} product - Produto específico
   * @returns {Object|null} Dados de comissão ou null se não encontrado
   */
  static getComissaoData(category, product) {
    return COMISSOES_CONFIG[category]?.[product] || null;
  }

  /**
   * Calcula valores de comissão baseado no valor investido
   * @param {number} valor - Valor investido
   * @param {string} category - Categoria do ativo
   * @param {string} product - Produto específico
   * @returns {Object|null} Valores calculados ou null
   */
  static calcularComissao(valor, category, product) {
    const comissaoData = this.getComissaoData(category, product);
    
    if (!comissaoData || valor <= 0) {
      return null;
    }

    return {
      valorMinimo: (valor * comissaoData.min) / 100,
      valorMaximo: (valor * comissaoData.max) / 100,
      taxaMinima: comissaoData.min,
      taxaMaxima: comissaoData.max,
      description: comissaoData.description,
      risco: comissaoData.risco
    };
  }

  /**
   * Obtém todas as categorias disponíveis
   * @returns {string[]} Array de categorias
   */
  static getCategorias() {
    return Object.keys(COMISSOES_CONFIG);
  }

  /**
   * Obtém todos os produtos de uma categoria
   * @param {string} category - Categoria
   * @returns {string[]} Array de produtos
   */
  static getProdutos(category) {
    return Object.keys(COMISSOES_CONFIG[category] || {});
  }

  /**
   * Obtém estatísticas gerais das comissões
   * @returns {Object} Estatísticas
   */
  static getEstatisticas() {
    const stats = {
      totalCategorias: 0,
      totalProdutos: 0,
      menorTaxa: Infinity,
      maiorTaxa: 0,
      taxaMedia: 0
    };

    let totalTaxas = 0;
    let countTaxas = 0;

    Object.keys(COMISSOES_CONFIG).forEach(category => {
      stats.totalCategorias++;
      
      Object.keys(COMISSOES_CONFIG[category]).forEach(product => {
        stats.totalProdutos++;
        
        const data = COMISSOES_CONFIG[category][product];
        
        // Atualizar menor e maior taxa
        if (data.min < stats.menorTaxa) stats.menorTaxa = data.min;
        if (data.max > stats.maiorTaxa) stats.maiorTaxa = data.max;
        
        // Calcular média
        totalTaxas += data.min + data.max;
        countTaxas += 2;
      });
    });

    stats.taxaMedia = countTaxas > 0 ? totalTaxas / countTaxas : 0;

    return stats;
  }

  /**
   * Valida se uma categoria e produto existem
   * @param {string} category - Categoria
   * @param {string} product - Produto
   * @returns {boolean} True se válido
   */
  static isValid(category, product) {
    return !!(COMISSOES_CONFIG[category]?.[product]);
  }

  /**
   * Obtém produtos por nível de risco
   * @param {string} risco - Nível de risco
   * @returns {Array} Array de produtos com esse nível de risco
   */
  static getProdutosPorRisco(risco) {
    const produtos = [];

    Object.keys(COMISSOES_CONFIG).forEach(category => {
      Object.keys(COMISSOES_CONFIG[category]).forEach(product => {
        const data = COMISSOES_CONFIG[category][product];
        if (data.risco === risco) {
          produtos.push({
            category,
            product,
            ...data
          });
        }
      });
    });

    return produtos;
  }

  /**
   * Obtém range de comissões para um conjunto de ativos
   * @param {Array} ativos - Array de {category, product, valor}
   * @returns {Object} Range total de comissões
   */
  static calcularRangeTotal(ativos) {
    let totalMinimo = 0;
    let totalMaximo = 0;

    ativos.forEach(ativo => {
      const comissao = this.calcularComissao(ativo.valor, ativo.category, ativo.product);
      if (comissao) {
        totalMinimo += comissao.valorMinimo;
        totalMaximo += comissao.valorMaximo;
      }
    });

    return {
      totalMinimo,
      totalMaximo,
      economia: totalMaximo - totalMinimo
    };
  }
}

export default COMISSOES_CONFIG;
