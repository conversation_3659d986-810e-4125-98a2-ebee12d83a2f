/**
 * Main Application
 * Integrates all extracted JavaScript modules and initializes them
 */
import { AssetSelectionFilterSystem } from './modules/asset-selection-filter.js';
import { AtivoColorTagAnimationSystem } from './modules/ativo-color-tag-animation.js';
import { AttributeFixerSystem } from './modules/attribute-fixer.js';
import { CurrencyControlSystem } from './modules/currency-control.js';
import { CurrencyFormattingSystem } from './modules/currency-formatting.js';
import { dgmCanvasIntegration } from './modules/dgm-canvas-integration.js';
import { eventCoordinator } from './modules/event-coordinator.js';
import { MotionAnimationSystem } from './modules/motion-animation.js';
import { PatrimonySyncSystem } from './modules/patrimony-sync.js';
import { ProductSystem } from './modules/product-system.js';
import { StepNavigationProgressSystem } from './modules/progress-bar-system.js';
import { ResultadoSyncSystem } from './modules/resultado-sync.js';
import { initSalesforceIntegration } from './modules/salesforce-integration.js';
import { SalesforceSyncSystem } from './modules/salesforce-sync.js';
import { ScrollFloatAnimationSystem } from './modules/scroll-float-animation.js';
import { SectionVisibilitySystem } from './modules/section-visibility.js';
import { SimpleSyncSystem } from './modules/simple-sync.js';
// import { StepNavigationSystem } from './modules/step-navigation.js';
import { TypebotIntegrationSystem } from './modules/typebot-integration.js';
import { WebflowButtonSystem } from './modules/webflow-button-integration.js';

/**
 * Main Application Class
 * Coordinates initialization and management of all systems
 */
class ReinoCalculatorApp {
  constructor() {
    // Initialize EventCoordinator first
    this.eventCoordinator = eventCoordinator;
    this.systems = {
      assetSelectionFilter: new AssetSelectionFilterSystem(),
      ativoColorTagAnimation: new AtivoColorTagAnimationSystem(),
      currencyControl: new CurrencyControlSystem(),
      currencyFormatting: new CurrencyFormattingSystem(),

      motionAnimation: new MotionAnimationSystem(),
      productSystem: new ProductSystem(),
      sectionVisibility: new SectionVisibilitySystem(),
      patrimonySync: new PatrimonySyncSystem(),
      simpleSync: new SimpleSyncSystem(),
      resultadoSync: new ResultadoSyncSystem(),
      attributeFixer: new AttributeFixerSystem(),
      stepNavigationProgress: new StepNavigationProgressSystem(),
      scrollFloatAnimation: new ScrollFloatAnimationSystem(),
      // stepNavigation: new StepNavigationSystem(),
      typebotIntegration: new TypebotIntegrationSystem(),
      webflowButton: new WebflowButtonSystem(),
    };

    this.isInitialized = false;
    this.initializationOrder = [
      'attributeFixer', // Fix attribute inconsistencies FIRST
      'assetSelectionFilter', // Asset selection system EARLY to setup checkboxes
      'ativoColorTagAnimation', // Tag animations AFTER asset selection
      'currencyFormatting', // Base currency system first
      'patrimonySync', // Core patrimony synchronization BEFORE currency controls
      'currencyControl', // Currency controls AFTER patrimony sync
      'simpleSync', // Simple synchronization for visual bars
      'resultadoSync', // Resultado section synchronization AFTER patrimony sync
      'motionAnimation', // Motion effects
      'productSystem', // Product interactions
      'stepNavigationProgress', // Step navigation and progress bar interactions
      'scrollFloatAnimation', // Scroll-triggered float animations
      'sectionVisibility', // Section visibility control
      // 'stepNavigation', // Step navigation system (replaced by stepNavigationProgress)
      'typebotIntegration', // Typebot integration BEFORE webflow buttons
      'webflowButton', // Webflow button integration AFTER step navigation and typebot
    ];
  }

  /**
   * Initialize all systems in the correct order
   */
  async init() {
    if (this.isInitialized) {
      return;
    }

    try {
      // Initialize systems in order
      for (const systemName of this.initializationOrder) {
        const system = this.systems[systemName];
        if (system && typeof system.init === 'function') {
          // Special case for webflowButton - pass stepNavigationProgress as parameter
          if (systemName === 'webflowButton') {
            await system.init(this.systems.stepNavigationProgress);
            // Connect Typebot integration to webflow button system
            system.setTypebotIntegration(this.systems.typebotIntegration);

            // Configure DGM Canvas integration
            const dgmEndpoint = this.getDGMCanvasEndpoint();

            // Debug log for endpoint configuration
            if (
              window.location.hostname === 'localhost' ||
              window.location.hostname === '127.0.0.1' ||
              window.location.hostname.includes('webflow.io') ||
              window.location.search.includes('debug=true')
            ) {
              // eslint-disable-next-line no-console
              console.log('🎨 [App] Configuring DGM Canvas with endpoint:', dgmEndpoint);
            }

            await system.configureDGMCanvas({
              endpoint: dgmEndpoint,
              timeout: 30000,
              retryAttempts: 3,
            });
          } else {
            await system.init();
          }
        }
      }

      // Configure system integrations after initialization
      this.configureSystemIntegrations();

      // Initialize Salesforce integration (independent of other systems)
      await initSalesforceIntegration();

      this.isInitialized = true;
      this.setupGlobalAPI();
      this.setupErrorHandling();

      // Dispatch ready event
      document.dispatchEvent(
        new CustomEvent('reinoCalculatorReady', {
          detail: {
            app: this,
            systems: this.systems,
          },
        })
      );
    } catch {
      // Silent error handling
    }
  }

  /**
   * Configure integrations between systems
   */
  configureSystemIntegrations() {
    // No additional integrations needed - stepNavigationProgress handles everything
    if (this.systems.stepNavigationProgress) {
      // Debug log for system
      if (
        window.location.hostname === 'localhost' ||
        window.location.hostname === '127.0.0.1' ||
        window.location.hostname.includes('webflow.io') ||
        window.location.search.includes('debug=true')
      ) {
        console.warn('🔗 [App] Step navigation progress system loaded');
      }
    }
  }

  /**
   * Get DGM Canvas endpoint configuration
   * @returns {string} Endpoint URL
   */
  getDGMCanvasEndpoint() {
    // Check if running in development or production
    const isDevelopment =
      window.location.hostname === 'localhost' ||
      window.location.hostname === '127.0.0.1' ||
      window.location.hostname.includes('webflow.io') || // Webflow preview
      window.location.search.includes('debug=true');

    if (isDevelopment) {
      return 'http://localhost:5173/api/data'; // DGM Canvas dev server
    }

    // Production endpoint - você pode configurar conforme necessário
    return 'https://your-dgm-canvas-production-url.com/api/data';
  }

  /**
   * Setup global API for debugging and external access
   */
  setupGlobalAPI() {
    window.ReinoCalculator = {
      app: this,
      systems: this.systems,

      // Utility methods
      restart: () => this.restart(),
      getSystemStatus: () => this.getSystemStatus(),

      // Debug utilities
      debugSync: () => {
        console.warn('🔍 REINO CALCULATOR - SYNCHRONIZATION DEBUG');
        console.warn('='.repeat(60));

        if (this.systems.simpleSync) {
          this.systems.simpleSync.debugFullStatus();
        } else {
          console.error('❌ SimpleSyncSystem not available');
        }

        if (this.systems.patrimonySync) {
          console.warn('\n💰 PATRIMONY SYNC STATUS:');
          console.warn(`  Main value: R$ ${this.systems.patrimonySync.getMainValue()}`);
          console.warn(`  Total allocated: R$ ${this.systems.patrimonySync.getTotalAllocated()}`);
          console.warn(`  Remaining: R$ ${this.systems.patrimonySync.getRemainingValue()}`);
        }

        if (this.systems.scrollFloatAnimation) {
          console.warn('\n🎬 SCROLL FLOAT ANIMATION STATUS:');
          const status = this.systems.scrollFloatAnimation.getStatus();
          console.warn('  isAnimated:', status.isAnimated, '| hasInView:', status.hasInView);
        }

        if (this.systems.resultadoSync) {
          console.warn('\n📊 RESULTADO SYNC STATUS:');
          const resultadoInfo = this.systems.resultadoSync.debugInfo();
          console.warn(`  Patrimônio items: ${resultadoInfo.patrimonioItems}`);
          console.warn(`  Resultado patrimônio items: ${resultadoInfo.resultadoPatrimonioItems}`);
          console.warn(`  Resultado comissão items: ${resultadoInfo.resultadoComissaoItems}`);
          console.warn(`  Total patrimônio: R$ ${resultadoInfo.totalPatrimonio}`);
        }

        console.warn('\n🎯 Quick Commands:');
        console.warn('  • ReinoCalculator.debugSync() - Full sync analysis');
        console.warn('  • ReinoCalculator.animation.scrollFloat.forceShow() - Force show float');
        console.warn('  • ReinoCalculator.data.sync.debugPairings() - Pairing details');
        console.warn('  • ReinoCalculator.data.sync.debugUnpairedElements() - Find orphans');
        console.warn('  • ReinoCalculator.data.sync.debugSyncTest() - Test synchronization');
        console.warn('  • ReinoCalculator.data.sync.debugCacheSync() - Cache restoration analysis');
        console.warn(
          '  • ReinoCalculator.data.sync.debugZeroingIssues() - Detect visual zeroing problems'
        );
        console.warn(
          '  • ReinoCalculator.data.sync.forceSyncFromSliders() - Force sync visuals from sliders'
        );
        console.warn('  • ReinoCalculator.data.resultado.debugInfo() - Resultado sync debug');
        console.warn('  • ReinoCalculator.data.resultado.testSync() - Test resultado sync');
        console.warn('  • ReinoCalculator.data.resultado.forceSync() - Force resultado sync');
      },

      // Quick fix for zeroing issues
      fixZeroing: () => {
        if (this.systems.simpleSync) {
          this.systems.simpleSync.forceSyncFromSliders();
        } else {
          console.error('❌ SimpleSyncSystem not available');
        }
      },

      // System controls
      currency: {
        control: this.systems.currencyControl,
        formatting: this.systems.currencyFormatting,
      },

      animation: {
        motion: this.systems.motionAnimation,
        scrollFloat: this.systems.scrollFloatAnimation,
      },

      ui: {
        products: this.systems.productSystem,
        visibility: this.systems.sectionVisibility,
      },

      data: {
        patrimony: this.systems.patrimonySync,
        sync: this.systems.simpleSync,
        resultado: this.systems.resultadoSync,
        attributeFixer: this.systems.attributeFixer,
      },

      navigation: {
        stepNavigation: this.systems.stepNavigationProgress,
      },

      // NEW: Typebot integration
      typebot: {
        integration: this.systems.typebotIntegration,
        status: () => this.systems.webflowButton?.getTypebotStatus() || {},
        enable: () => this.systems.webflowButton?.setTypebotEnabled(true),
        disable: () => this.systems.webflowButton?.setTypebotEnabled(false),
      },
    };
  }

  /**
   * Setup global error handling
   */
  setupErrorHandling() {
    window.addEventListener('error', () => {
      // console.error('Reino Calculator Error:', event.error);
    });

    window.addEventListener('unhandledrejection', () => {
      // console.error('Reino Calculator Unhandled Promise Rejection:', event.reason);
    });
  }

  /**
   * Get status of all systems
   */
  getSystemStatus() {
    const status = {};

    for (const [name, system] of Object.entries(this.systems)) {
      status[name] = {
        initialized: system.isInitialized || false,
        available: typeof system.init === 'function',
      };
    }

    return status;
  }

  /**
   * Restart the application
   */
  async restart() {
    // console.log('Restarting Reino Calculator App...');

    // Cleanup existing systems
    for (const system of Object.values(this.systems)) {
      if (typeof system.cleanup === 'function') {
        system.cleanup();
      }
    }

    this.isInitialized = false;

    // Reinitialize
    await this.init();
  }

  /**
   * Cleanup all systems
   */
  cleanup() {
    for (const system of Object.values(this.systems)) {
      if (typeof system.cleanup === 'function') {
        system.cleanup();
      }
    }

    this.isInitialized = false;
    delete window.ReinoCalculator;

    // console.log('Reino Calculator App cleaned up');
  }
}

// Create and initialize the application
const app = new ReinoCalculatorApp();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => app.init());
} else {
  app.init();
}

// Cleanup on page unload
window.addEventListener('beforeunload', () => app.cleanup());

// Export for manual control if needed
export { ReinoCalculatorApp };
